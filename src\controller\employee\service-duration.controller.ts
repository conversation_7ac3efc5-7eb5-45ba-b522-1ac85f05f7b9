import { Controller, Post, Get, Body, Param, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';

import {
  ServiceDurationRecordService,
  StartServiceData,
  EndServiceData,
} from '../../service/service-duration-record.service';
import { ServiceDurationRecordType } from '../../entity/service-duration-record.entity';

/**
 * 开始服务DTO
 * 用于员工手动开始特定服务项目的计时
 */
export class StartServiceDTO {
  /** 订单ID */
  orderId: number;
  /** 订单详情ID（主服务时必填） */
  orderDetailId?: number;
  /** 追加服务订单ID（增项服务时必填） */
  additionalServiceOrderId?: number;
  /** 记录类型：main_service(主服务) 或 additional_service(增项服务) */
  recordType: ServiceDurationRecordType;
  /** 服务ID（主服务时必填） */
  serviceId?: number;
  /** 增项服务ID（增项服务时必填） */
  additionalServiceId?: number;
  /** 备注信息 */
  remark?: string;
}

/**
 * 结束服务DTO
 * 用于员工结束特定服务项目的计时
 */
export class EndServiceDTO {
  /** 服务时长记录ID */
  recordId: number;
  /** 备注信息 */
  remark?: string;
}

/**
 * 员工端服务时长管理控制器
 *
 * 功能说明：
 * 1. 服务时间分为总时间、服务项时间和增项服务时间
 * 2. 增项服务可设置是否需要统计时间（needDurationTracking字段）
 * 3. 员工点开始服务时，整体订单进入服务中状态，同步开始服务项的计时
 * 4. 服务项完成后记录结束时间，若没有增项或所有增项都完成，则订单自动结束
 * 5. 若有需要统计时长的增项，员工需手动开始增项服务计时
 * 6. 所有服务项和需要统计时长的增项服务完成后，订单自动结束并记录总时长
 */
@Controller('/employee/service-duration')
export class EmployeeServiceDurationController {
  @Inject()
  ctx: Context;

  @Inject()
  serviceDurationRecordService: ServiceDurationRecordService;

  /**
   * 开始单个服务项目计时
   * 用于员工手动开始特定的主服务或增项服务
   * 注意：只有needDurationTracking=true的增项服务才能开始计时
   */
  @Post('/start', { summary: '开始单个服务项目计时' })
  @Validate()
  async startService(@Body() body: StartServiceDTO) {
    const employeeId = this.ctx.state.user.userId;

    const data: StartServiceData = {
      orderId: body.orderId,
      orderDetailId: body.orderDetailId,
      additionalServiceOrderId: body.additionalServiceOrderId,
      recordType: body.recordType,
      serviceId: body.serviceId,
      additionalServiceId: body.additionalServiceId,
      remark: body.remark,
    };

    return await this.serviceDurationRecordService.startService(
      employeeId,
      data
    );
  }

  /**
   * 结束单个服务项目计时
   * 完成服务项目并记录时长，系统会自动检查是否需要结束整个订单
   */
  @Post('/end', { summary: '结束单个服务项目计时' })
  @Validate()
  async endService(@Body() body: EndServiceDTO) {
    const employeeId = this.ctx.state.user.userId;

    const data: EndServiceData = {
      recordId: body.recordId,
      remark: body.remark,
    };

    return await this.serviceDurationRecordService.endService(employeeId, data);
  }

  /**
   * 查询指定订单的所有服务时长记录
   * 包含主服务和增项服务的详细计时信息
   */
  @Get('/records/:orderId', { summary: '查询订单服务时长记录' })
  async getOrderServiceRecords(@Param('orderId') orderId: number) {
    return await this.serviceDurationRecordService.getServiceDurationRecords(
      orderId
    );
  }

  /**
   * 获取当前员工正在进行中的所有服务
   * 显示未完成的服务项目和已进行的时长
   */
  @Get('/current', { summary: '获取当前进行中的服务' })
  async getCurrentServices() {
    const employeeId = this.ctx.state.user.userId;
    return await this.serviceDurationRecordService.getCurrentServices(
      employeeId
    );
  }

  /**
   * 查询当前员工的服务时长记录
   * 可指定订单ID查询特定订单，否则返回当前进行中的服务
   */
  @Get('/my-records', { summary: '查询我的服务时长记录' })
  async getMyServiceRecords(@Query('orderId') orderId?: number) {
    const employeeId = this.ctx.state.user.userId;

    if (orderId) {
      return await this.serviceDurationRecordService.getServiceDurationRecords(
        orderId,
        employeeId
      );
    }

    // 如果没有指定订单ID，返回当前进行中的服务
    return await this.serviceDurationRecordService.getCurrentServices(
      employeeId
    );
  }

  /**
   * 开始整体订单服务
   * 这是服务流程的入口，会：
   * 1. 将订单状态更新为"服务中"
   * 2. 自动开始所有主服务项目的计时
   * 3. 记录实际服务开始时间
   * 4. 上传服务前照片（可选）
   */
  @Post('/start-order-service', { summary: '开始整体订单服务' })
  @Validate()
  async startOrderService(
    @Body() body: { orderId: number; beforePhotos?: string[] }
  ) {
    const employeeId = this.ctx.state.user.userId;

    return await this.serviceDurationRecordService.startOrderService(
      employeeId,
      body.orderId,
      body.beforePhotos
    );
  }

  /**
   * 查询订单服务状态和所有服务项目
   * 返回主服务和增项服务的完整状态信息，包括：
   * - 各服务项目的完成状态
   * - 时长统计信息
   * - 订单整体服务进度
   */
  @Get('/order-service-status/:orderId', {
    summary: '查询订单服务状态和所有服务项目',
  })
  async getOrderServiceStatus(@Param('orderId') orderId: number) {
    const employeeId = this.ctx.state.user.userId;

    return await this.serviceDurationRecordService.getOrderServiceStatus(
      orderId,
      employeeId
    );
  }
}
