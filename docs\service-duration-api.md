# 服务时间统计 API 接口文档

## 员工端接口

### 1. 开始整体订单服务

**接口地址**：`POST /employee/service-duration/start-order-service`

**接口描述**：开始整体订单服务，将订单状态更新为"服务中"并自动开始所有主服务计时

**请求参数**：
```json
{
  "orderId": 123,
  "beforePhotos": ["photo1.jpg", "photo2.jpg"]  // 可选，服务前照片
}
```

**响应示例**：
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "orderId": 123,
    "orderStatus": "服务中",
    "actualServiceStartTime": "2024-01-01T10:00:00.000Z",
    "mainServiceRecords": [
      {
        "id": 1,
        "orderId": 123,
        "orderDetailId": 456,
        "serviceId": 789,
        "serviceName": "基础洗护",
        "startTime": "2024-01-01T10:00:00.000Z"
      }
    ],
    "message": "成功开始订单服务，自动开始了 2 个主服务项目"
  }
}
```

### 2. 开始单个服务项目计时

**接口地址**：`POST /employee/service-duration/start`

**接口描述**：手动开始特定的主服务或增项服务计时

**请求参数**：
```json
{
  "orderId": 123,
  "recordType": "additional_service",  // main_service 或 additional_service
  "additionalServiceOrderId": 456,    // 增项服务时必填
  "additionalServiceId": 789,         // 增项服务时必填
  "remark": "开始美容服务"             // 可选
}
```

**主服务请求示例**：
```json
{
  "orderId": 123,
  "recordType": "main_service",
  "orderDetailId": 456,
  "serviceId": 789,
  "remark": "开始洗护服务"
}
```

**响应示例**：
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "orderId": 123,
    "recordType": "additional_service",
    "additionalServiceId": 789,
    "serviceName": "美容服务",
    "startTime": "2024-01-01T10:30:00.000Z",
    "remark": "开始美容服务"
  }
}
```

### 3. 结束单个服务项目计时

**接口地址**：`POST /employee/service-duration/end`

**接口描述**：结束单个服务项目计时，记录时长并检查是否需要自动完成订单

**请求参数**：
```json
{
  "recordId": 123,
  "remark": "服务完成"  // 可选
}
```

**响应示例**：
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "id": 123,
    "orderId": 456,
    "serviceName": "美容服务",
    "startTime": "2024-01-01T10:30:00.000Z",
    "endTime": "2024-01-01T11:00:00.000Z",
    "duration": 30,  // 分钟
    "remark": "服务完成"
  }
}
```

### 4. 查询订单服务状态

**接口地址**：`GET /employee/service-duration/order-service-status/{orderId}`

**接口描述**：查询订单服务状态和所有服务项目的完成情况

**响应示例**：
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "orderId": 123,
    "orderSn": "ORD20240101001",
    "orderStatus": "服务中",
    "actualServiceStartTime": "2024-01-01T10:00:00.000Z",
    "actualServiceEndTime": null,
    "actualServiceDuration": 60,
    "customer": {
      "id": 1,
      "nickname": "张三",
      "phone": "13800138000"
    },
    "employee": {
      "id": 1,
      "name": "李师傅",
      "phone": "13900139000"
    },
    "mainServices": [
      {
        "orderDetailId": 456,
        "serviceId": 789,
        "serviceName": "基础洗护",
        "servicePrice": 100,
        "petName": "小白",
        "avgDuration": 45,
        "status": "completed",  // not_started, in_progress, completed
        "serviceRecord": {
          "id": 1,
          "startTime": "2024-01-01T10:00:00.000Z",
          "endTime": "2024-01-01T10:45:00.000Z",
          "duration": 45
        }
      }
    ],
    "additionalServices": [
      {
        "additionalServiceOrderId": 123,
        "serviceId": 456,
        "serviceName": "美容服务",
        "servicePrice": 80,
        "quantity": 1,
        "status": "in_progress",
        "serviceRecord": {
          "id": 2,
          "startTime": "2024-01-01T10:30:00.000Z",
          "endTime": null,
          "duration": null
        }
      }
    ],
    "serviceStatistics": {
      "totalRecords": 2,
      "completedRecords": 1,
      "inProgressRecords": 1,
      "totalDuration": 45,
      "mainServiceDuration": 45,
      "additionalServiceDuration": 0
    },
    "summary": {
      "totalMainServices": 1,
      "totalAdditionalServices": 1,
      "completedMainServices": 1,
      "completedAdditionalServices": 0,
      "inProgressMainServices": 0,
      "inProgressAdditionalServices": 1
    }
  }
}
```

### 5. 获取当前进行中的服务

**接口地址**：`GET /employee/service-duration/current`

**接口描述**：获取当前员工正在进行中的所有服务

**响应示例**：
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "employeeId": 1,
    "currentServices": [
      {
        "id": 2,
        "orderId": 123,
        "recordType": "additional_service",
        "serviceName": "美容服务",
        "startTime": "2024-01-01T10:30:00.000Z",
        "currentDuration": 15,  // 当前已进行时长（分钟）
        "expectedDuration": 30, // 预期时长
        "order": {
          "id": 123,
          "sn": "ORD20240101001",
          "status": "服务中",
          "customer": {
            "id": 1,
            "nickname": "张三",
            "phone": "13800138000"
          }
        }
      }
    ],
    "totalCurrentServices": 1
  }
}
```

### 6. 查询服务时长记录

**接口地址**：`GET /employee/service-duration/records/{orderId}`

**接口描述**：查询指定订单的所有服务时长记录

**响应示例**：
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "orderId": 123,
    "records": [
      {
        "id": 1,
        "recordType": "main_service",
        "serviceName": "基础洗护",
        "startTime": "2024-01-01T10:00:00.000Z",
        "endTime": "2024-01-01T10:45:00.000Z",
        "duration": 45,
        "employee": {
          "id": 1,
          "name": "李师傅",
          "phone": "13900139000"
        },
        "service": {
          "id": 789,
          "serviceName": "基础洗护",
          "avgDuration": 45
        }
      }
    ],
    "statistics": {
      "totalRecords": 2,
      "completedRecords": 1,
      "inProgressRecords": 1,
      "totalDuration": 45,
      "mainServiceDuration": 45,
      "additionalServiceDuration": 0,
      "mainServiceCount": 1,
      "additionalServiceCount": 1
    }
  }
}
```

### 7. 查询我的服务时长记录

**接口地址**：`GET /employee/service-duration/my-records?orderId={orderId}`

**接口描述**：查询当前员工的服务时长记录，可指定订单ID

**查询参数**：
- `orderId`（可选）：指定订单ID，不传则返回当前进行中的服务

**响应示例**：与接口6相同

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 1001 | 订单不存在 | 指定的订单ID不存在 |
| 1002 | 无权限操作此订单 | 当前员工不是该订单的负责员工 |
| 1003 | 订单状态不正确 | 订单状态不允许当前操作 |
| 1004 | 该服务项目已开始 | 服务项目已经开始，不能重复开始 |
| 1005 | 服务不存在 | 指定的服务ID不存在 |
| 1006 | 该增项服务不需要统计时长 | 增项服务的needDurationTracking为false |
| 1007 | 服务记录不存在 | 指定的服务记录ID不存在 |
| 1008 | 该服务已结束 | 服务已经结束，不能重复结束 |
| 1009 | 服务尚未开始 | 服务还没有开始，不能结束 |
