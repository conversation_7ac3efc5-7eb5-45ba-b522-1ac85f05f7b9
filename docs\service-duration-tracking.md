# 服务时间统计功能使用说明

## 功能概述

服务时间统计功能用于记录和管理宠物服务过程中的时间消耗，支持主服务和增项服务的分别计时，并提供自动订单完成判断机制。

## 核心概念

### 时间类型分类
1. **总时间**：整个订单从开始到结束的总服务时长
2. **服务项时间**：每个具体服务项目的执行时长
3. **增项服务时间**：额外增加服务的执行时长

### 服务类型
1. **主服务（main_service）**：订单中的基础服务项目，必须完成
2. **增项服务（additional_service）**：订单中的额外服务项目，可选择是否需要统计时长

### 时长统计控制
- 增项服务通过 `needDurationTracking` 字段控制是否需要统计时长
- 只有 `needDurationTracking=true` 的增项服务才会影响订单完成状态
- 不需要统计时长的增项服务不会产生时长记录

## 业务流程

### 1. 开始整体订单服务
**接口**：`POST /employee/service-duration/start-order-service`

**功能**：
- 将订单状态从"已出发"或"待服务"更新为"服务中"
- 记录实际服务开始时间
- 自动开始所有主服务项目的计时
- 可选择上传服务前照片

**请求参数**：
```json
{
  "orderId": 123,
  "beforePhotos": ["photo1.jpg", "photo2.jpg"]  // 可选
}
```

### 2. 手动开始单个服务项目
**接口**：`POST /employee/service-duration/start`

**功能**：
- 员工手动开始特定的主服务或增项服务计时
- 只有需要统计时长的增项服务才能开始计时

**请求参数**：
```json
{
  "orderId": 123,
  "recordType": "additional_service",
  "additionalServiceOrderId": 456,
  "additionalServiceId": 789,
  "remark": "开始美容服务"
}
```

### 3. 结束服务项目
**接口**：`POST /employee/service-duration/end`

**功能**：
- 结束单个服务项目的计时
- 记录服务时长和结束时间
- 自动检查是否需要结束整个订单

**请求参数**：
```json
{
  "recordId": 123,
  "remark": "服务完成"
}
```

### 4. 自动订单完成判断
当服务项目完成时，系统会自动检查：
1. 所有主服务是否都已完成
2. 所有需要统计时长的增项服务是否都已完成
3. 如果都完成，则自动调用订单完成接口

## 查询接口

### 1. 查询订单服务状态
**接口**：`GET /employee/service-duration/order-service-status/{orderId}`

**返回信息**：
- 订单基本信息和状态
- 所有主服务的完成状态
- 所有增项服务的完成状态
- 时长统计信息

### 2. 查询当前进行中的服务
**接口**：`GET /employee/service-duration/current`

**返回信息**：
- 当前员工正在进行的所有服务
- 每个服务的已进行时长
- 预期完成时长

### 3. 查询服务时长记录
**接口**：`GET /employee/service-duration/records/{orderId}`

**返回信息**：
- 指定订单的所有服务时长记录
- 按服务类型分组的统计信息
- 总时长和分类时长统计

## 数据模型

### ServiceDurationRecord（服务时长记录）
```typescript
{
  id: number;                    // 记录ID
  orderId: number;               // 订单ID
  orderDetailId?: number;        // 订单详情ID（主服务）
  additionalServiceOrderId?: number; // 增项服务订单ID
  employeeId: number;            // 员工ID
  recordType: 'main_service' | 'additional_service'; // 记录类型
  serviceId?: number;            // 服务ID
  serviceName: string;           // 服务名称
  additionalServiceId?: number;  // 增项服务ID
  startTime?: Date;              // 开始时间
  endTime?: Date;                // 结束时间
  duration?: number;             // 时长（分钟）
  remark?: string;               // 备注
}
```

### AdditionalService（增项服务）
```typescript
{
  id: number;
  name: string;
  duration?: number;             // 预期时长（分钟）
  needDurationTracking: boolean; // 是否需要统计时长
  // ... 其他字段
}
```

## 注意事项

1. **权限控制**：只有订单对应的员工才能操作该订单的服务时长记录
2. **状态检查**：只有"服务中"状态的订单才能进行服务计时操作
3. **重复开始检查**：同一个服务项目不能重复开始，必须先结束当前服务
4. **自动完成**：系统会自动判断订单是否可以完成，无需手动干预
5. **平均时长更新**：每次服务完成后会异步更新该服务的平均时长，用于优化时间预估
6. **错误处理**：所有异步操作都有错误处理，不会影响主流程

## 常见问题

### Q: 增项服务什么时候需要手动开始？
A: 只有 `needDurationTracking=true` 的增项服务需要手动开始计时，其他增项服务不影响订单完成状态。

### Q: 订单什么时候会自动完成？
A: 当所有主服务和所有需要统计时长的增项服务都完成后，订单会自动完成。

### Q: 如果员工忘记结束某个服务怎么办？
A: 可以通过查询当前进行中的服务接口找到未完成的服务，然后手动结束。

### Q: 服务时长统计的精度是多少？
A: 时长以分钟为单位，四舍五入到最近的分钟。

### Q: 平均时长是如何计算的？
A: 基于最近10次该服务的完成记录计算平均值，用于优化服务时间预估。
